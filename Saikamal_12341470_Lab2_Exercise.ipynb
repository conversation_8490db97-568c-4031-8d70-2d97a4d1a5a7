#hamiltonian cycle via Depth first search branch and bound

def weighted_to_unweighted(weighted_graph):
    """Convert weighted adjacency matrix -> unweighted (0/1)."""
    n = len(weighted_graph)
    adj = [[0]*n for _ in range(n)]
    for i in range(n):
        for j in range(n):
            if weighted_graph[i][j] != 0:   # edge exists if weight ≠ 0
                adj[i][j] = 1
    return adj


def is_hamiltonian_cycle(adj):
    """
    Check Hamiltonian cycle using backtracking.
    Returns (True, cycle) if found, else (False, None).
    """
    n = len(adj)
    path = [0]
    used = [False]*n
    used[0] = True

    def backtrack():
        if len(path) == n:
            return adj[path[-1]][path[0]] == 1
        for v in range(1, n):
            if not used[v] and adj[path[-1]][v] == 1:
                used[v] = True
                path.append(v)
                if backtrack():
                    return True
                path.pop()
                used[v] = False
        return False

    if backtrack():
        return True, path + [path[0]]
    return False, None


def is_hamiltonian_path(adj):
    """
    Check Hamiltonian path (not necessarily a cycle).
    Returns (True, path) if found, else (Fals<PERSON>, None).
    """
    n = len(adj)

    def try_start(s):
        path = [s]
        used = [False]*n
        used[s] = True

        def backtrack():
            if len(path) == n:
                return True
            for v in range(n):
                if not used[v] and adj[path[-1]][v] == 1:
                    used[v] = True
                    path.append(v)
                    if backtrack():
                        return True
                    path.pop()
                    used[v] = False
            return False

        return (backtrack(), path)

    for s in range(n):
        ok, p = try_start(s)
        if ok:
            return True, p
    return False, None

if __name__ == "__main__":
    # Weighted adjacency matrix (undirected graph)
    weighted_graph = [
        [0, 2, 0, 1],
        [2, 0, 3, 0],
        [0, 3, 0, 1],
        [6, 0, 1, 0]
    ]

    print("Weighted Graph (Adjacency Matrix):")
    for row in weighted_graph:
        print(row)

    # Convert to unweighted
    unweighted = weighted_to_unweighted(weighted_graph)

    print("\nUnweighted Graph (Adjacency Matrix):")
    for row in unweighted:
        print(row)

    # Check Hamiltonian Cycle
    has_cycle, cycle = is_hamiltonian_cycle(unweighted)
    print("\nHamiltonian Cycle Exists?:", has_cycle)
    if has_cycle:
        print("Cycle:", cycle)

    # Check Hamiltonian Path
    has_path, path = is_hamiltonian_path(unweighted)
    print("\nHamiltonian Path Exists?:", has_path)
    if has_path:
        print("Path:", path)


import math

def tsp_branch_and_bound(weighted_graph):
    n = len(weighted_graph)
    global_best_cost = math.inf
    global_best_path = []
    global_best_start = None

    def dfs(path, cost, visited, start):
        nonlocal best_cost, best_path
        current = path[-1]

        if cost >= best_cost:
            return


        if len(path) == n:
            if weighted_graph[current][start] != 0:  
                total_cost = cost + weighted_graph[current][start]
                if total_cost < best_cost:
                    best_cost = total_cost
                    best_path = path[:] + [start]
            return


        for v in range(n):
            if not visited[v] and weighted_graph[current][v] != 0:
                visited[v] = True
                dfs(path + [v], cost + weighted_graph[current][v], visited, start)
                visited[v] = False


    for start in range(n):
        best_cost = math.inf
        best_path = []
        visited = [False]*n
        visited[start] = True
        dfs([start], 0, visited, start)

        print(f"Best cycle starting at {start}: cost = {best_cost}, path = {best_path}")

        if best_cost < global_best_cost:
            global_best_cost = best_cost
            global_best_path = best_path
            global_best_start = start

    return global_best_cost, global_best_path, global_best_start



if __name__ == "__main__":
    
    best_cost, best_path, best_start = tsp_branch_and_bound(weighted_graph)

    print("\nOverall Optimal Cycle (TSP):")
    print("Starting Point:", best_start)
    print("Cost:", best_cost)
    print("Path:", best_path)
