import random

N = 8  # chessboard size (8x8)



# Convert matrix (0/1 board) -> 1D list representation
def matrix_to_list(matrix):
    board = [-1] * N
    for r in range(N):
        for c in range(N):
            if matrix[r][c] == 1:
                board[c] = r
    return board

# Convert 1D list -> matrix (0/1 board)
def list_to_matrix(board):
    matrix = [[0 for _ in range(N)] for _ in range(N)]
    for c, r in enumerate(board):
        matrix[r][c] = 1
    return matrix   

# ------------------- Conflict counter -------------------
def count_conflicts(board):
    conflicts = 0
    for i in range(N):
        for j in range(i + 1, N):
            same_row = (board[i] == board[j])
            same_diag = (abs(board[i] - board[j]) == abs(i - j))
            if same_row or same_diag:
                conflicts += 1
    return conflicts

# ------------------- Min-conflict step count -------------------

def new_min_conflict(board):
    col = random.randint(0, N - 1)  # pick a random column
    best_row = board[col]  #current row of that queen
    best_conflict = count_conflicts(board)  #conflicts of the current board.

# Move one queen  to  find the best row
    for row in range(N):
        if row == board[col]:
            continue
        new_board = board[:]
        new_board[col] = row
        current_conflict = count_conflicts(new_board)
        if current_conflict < best_conflict:
            best_conflict = current_conflict
            best_row = row

    board[col] = best_row
    return board, best_conflict

# Gradient descent with random restart
def solve(initial_matrix=None):
    attempts = 0
    while True:
        attempts += 1

        # if user gives matrix then convert , else create random board
        if initial_matrix is None:
            board = [random.randint(0, N - 1) for _ in range(N)]
        else:
            board = matrix_to_list(initial_matrix)

        conflicts = count_conflicts(board)
        print(f"\n🔄 Restart {attempts}: start conflicts = {conflicts}, board = {board}")

        steps = 0
        while steps < 500:
            if conflicts == 0:
                print(f"✅ Solution found in {steps} steps after {attempts} restarts")
                return board

            board, new_conflicts = new_min_conflict(board)

            if new_conflicts == conflicts:
                print(f"⚠️ Stuck at local minimum (conflicts = {conflicts}) → restart")
                break # give up and restart

            conflicts = new_conflicts #Update the conflict number so we can compare again in the next round
            steps += 1

# ------------------- Print final board -------------------
def print_board(board):
    matrix = list_to_matrix(board)
    for row in matrix:
        print(row)

# ------------------- Starting board-----------------


initial_matrix = [
    [0,0,0,0,0,0,0,0],
    [0,0,0,0,1,0,0,0],
    [0,0,0,0,0,0,0,1],
    [0,0,0,0,0,1,0,0],
    [0,0,1,0,0,0,0,0],
    [0,0,0,0,0,0,1,0],
    [0,1,0,0,0,0,0,0],
    [1,0,0,1,0,0,0,0],
]

solution = solve(initial_matrix)
print("\nFinal solution (list form):", solution)
if solution:
    print("\nFinal board:")
    print_board(solution)
else:
    print("No valid solution found in this run.")

from google.colab import drive
drive.mount('/content/drive')

