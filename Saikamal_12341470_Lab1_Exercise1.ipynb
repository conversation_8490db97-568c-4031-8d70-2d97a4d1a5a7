{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c6466b72", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Goal state:\n", "1 2 3\n", "4 5 6\n", "7 8  \n", "\n", "Start state:\n", "1 2 3\n", "4   6\n", "7 5 8\n"]}], "source": ["import numpy as np\n", "import random\n", "import heapq\n", "\n", "nums=list(range(9))\n", "\"\"\"\n", "# this is for random input and finding its output via astar\n", "random.shuffle(nums)\n", "grid=np.array(nums).reshape(3,3)\n", "print(grid)  \n", "\"\"\"\n", "grid = np.array([[1, 2, 3],\n", "                 [4, 0, 6],\n", "                 [7, 5, 8]])\n", "\n", "\n", "true=1\n", "true_grid=np.zeros((3,3),dtype=int)\n", "\n", "for i in range(3):\n", "    for j in range(3):\n", "        true_grid[i][j]=true\n", "        true+=1\n", "        if(true==9):\n", "            true=0\n", "print(\"Goal state:\")\n", "for row in true_grid:\n", "    print(\" \".join(str(x) if x != 0 else \" \" for x in row))\n", "\n", "print(\"\\nStart state:\")\n", "for row in grid:\n", "    print(\" \".join(str(x) if x != 0 else \" \" for x in row))"]}, {"cell_type": "code", "execution_count": 4, "id": "5897e80a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Step 0 (f=2):\n", "1 2 3\n", "4   6\n", "7 5 8\n", "\n", "Step 1 (f=2):\n", "1 2 3\n", "4 5 6\n", "7   8\n", "\n", "Step 2 (f=2):\n", "1 2 3\n", "4 5 6\n", "7 8  \n"]}], "source": ["def manhattan_distance(point1, point2):\n", "    return abs(point1[0] - point2[0]) + abs(point1[1] - point2[1])\n", "\n", "def calculate_heuristics(grid, true_grid):#sums the manhattan distance of all the tiles\n", "    h_cost = 0\n", "    for i in range(3):\n", "        for j in range(3):\n", "            if grid[i][j] != 0:\n", "                point1 = (i, j)\n", "                point2 = np.where(true_grid == grid[i][j])\n", "                goal_pos = (point2[0][0], point2[1][0])\n", "                h_cost += manhattan_distance(point1, goal_pos)\n", "    return h_cost\n", "\n", "def generate_neighbors(state):\n", "    surround=[]\n", "    grid=np.array(state).reshape(3,3)\n", "    x,y = np.where(grid == 0)\n", "    x,y=int(x[0]),int(y[0])\n", "    moves=[(-1,0),(1,0),(0,-1),(0,1)]\n", "    for dx,dy in moves:\n", "        nx,ny=x+dx,y+dy #nx and ny represent neighbour coordinates\n", "        if 0<=nx<3 and 0<=ny<3:\n", "            new_grid=grid.copy()\n", "            new_grid[x, y], new_grid[nx, ny] = new_grid[nx, ny], new_grid[x, y]\n", "            surround.append(tuple(new_grid.flatten()))\n", "    return surround\n", "\n", "def astar(grid, true_grid):\n", "    open_set = []\n", "    closed_set = set()\n", "    start_state = tuple(grid.flatten())\n", "    goal_state = tuple(true_grid.flatten())\n", "    g_cost = 0\n", "    h_cost = calculate_heuristics(grid, true_grid)\n", "    heapq.heappush(open_set, (g_cost + h_cost, g_cost, start_state))\n", "\n", "    while open_set:\n", "        f_cost, g_cost, current_state = heapq.heappop(open_set)\n", "        print(f\"\\nStep {g_cost} (f={f_cost}):\")\n", "        for row in np.array(current_state).reshape(3, 3):\n", "            print(\" \".join(str(x) if x != 0 else \" \" for x in row))\n", "\n", "\n", "        if current_state == goal_state:\n", "            return g_cost, np.array(current_state).reshape(3, 3)\n", "\n", "        if current_state in closed_set:\n", "            continue\n", "        closed_set.add(current_state)\n", "\n", "        for neighbour in generate_neighbors(current_state):\n", "            if neighbour not in closed_set:\n", "                new_g_cost = g_cost + 1\n", "                new_h_cost = calculate_heuristics(np.array(neighbour).reshape(3, 3), true_grid)\n", "                new_f_cost = new_g_cost + new_h_cost\n", "                heapq.heappush(open_set, (new_f_cost, new_g_cost, neighbour))\n", "\n", "    return None, None\n", "\n", "\n", "\n", "ans_grid=astar(grid,true_grid)\n", "\n", "    \n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "4cecfe77", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Step 0 (f=2):\n", "1 2 3\n", "4   6\n", "7 5 8\n", "\n", "Step 1 (f=2):\n", "1 2 3\n", "4 5 6\n", "7   8\n", "\n", "Step 2 (f=2):\n", "1 2 3\n", "4 5 6\n", "7 8  \n"]}, {"data": {"text/plain": ["(2,\n", " array([[1, 2, 3],\n", "        [4, 5, 6],\n", "        [7, 8, 0]]))"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["#heuristics function:2--missing tiles heuristic \n", "\n", "def calculate_heuristics(grid, true_grid):  # misplaced tiles heuristic\n", "    h_cost = 0\n", "    for i in range(3):\n", "        for j in range(3):\n", "            if grid[i][j] != 0 and grid[i][j] != true_grid[i][j]:\n", "                h_cost += 1\n", "    return h_cost\n", "\n", "def generate_neighbors(state):\n", "    surround = []\n", "    grid = np.array(state).reshape(3, 3)\n", "    x, y = np.where(grid == 0)\n", "    x, y = int(x[0]), int(y[0])\n", "    moves = [(-1,0), (1,0), (0,-1), (0,1)]\n", "    for dx, dy in moves:\n", "        nx, ny = x + dx, y + dy\n", "        if 0 <= nx < 3 and 0 <= ny < 3:\n", "            new_grid = grid.copy()\n", "            new_grid[x, y], new_grid[nx, ny] = new_grid[nx, ny], new_grid[x, y]\n", "            surround.append(tuple(new_grid.flatten()))\n", "    return surround\n", "\n", "def astar_missing(grid, true_grid):\n", "    open_set = []\n", "    closed_set = set()\n", "    start_state = tuple(grid.flatten())\n", "    goal_state = tuple(true_grid.flatten())\n", "    g_cost = 0\n", "    h_cost = calculate_heuristics(grid, true_grid)\n", "    heapq.heappush(open_set, (g_cost + h_cost, g_cost, start_state))\n", "\n", "    while open_set:\n", "        f_cost, g_cost, current_state = heapq.heappop(open_set)\n", "        print(f\"\\nStep {g_cost} (f={f_cost}):\")\n", "        for row in np.array(current_state).reshape(3, 3):\n", "            print(\" \".join(str(x) if x != 0 else \" \" for x in row))\n", "\n", "        if current_state == goal_state:\n", "            return g_cost, np.array(current_state).reshape(3, 3)\n", "\n", "        if current_state in closed_set:\n", "            continue\n", "        closed_set.add(current_state)\n", "\n", "        for neighbour in generate_neighbors(current_state):\n", "            if neighbour not in closed_set:\n", "                new_g_cost = g_cost + 1\n", "                new_h_cost = calculate_heuristics(np.array(neighbour).reshape(3, 3), true_grid)\n", "                new_f_cost = new_g_cost + new_h_cost\n", "                heapq.heappush(open_set, (new_f_cost, new_g_cost, neighbour))\n", "\n", "    return None, None\n", "\n", "astar_missing(grid,true_grid)\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON> (3.12.3)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}